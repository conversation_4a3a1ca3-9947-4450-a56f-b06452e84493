{"extends": ["next/core-web-vitals", "plugin:import/recommended", "plugin:import/typescript", "prettier", "plugin:tailwindcss/recommended"], "plugins": ["tailwindcss"], "rules": {"tailwindcss/no-custom-classname": "off", "tailwindcss/classnames-order": "off", "@next/next/no-img-element": "off"}, "settings": {"import/resolver": {"typescript": {"alwaysTryTypes": true}}}, "ignorePatterns": ["**/components/ui/**"]}